#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星风险评估服务
负责投资风险的评估和控制
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

# 导入四大核心系统（修复路径）
try:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
    core_path = os.path.join(backend_dir, "core")

    if core_path not in sys.path:
        sys.path.insert(0, core_path)

    from domain.memory.legendary.interface import legendary_memory_interface
    from domain.memory.legendary.models import MessageType, MemoryScope, MemoryPriority
    from roles.tianji_star.config.deepseek_config import get_memory_config
    CORE_SYSTEMS_AVAILABLE = True
except ImportError:
    legendary_memory_interface = None
    MessageType = None
    get_memory_config = None
    CORE_SYSTEMS_AVAILABLE = False

try:
    from performance.star_performance_monitor import StarPerformanceMonitor
    performance_monitor = StarPerformanceMonitor()
except ImportError:
    performance_monitor = None

try:
    from enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
except ImportError:
    EnhancedSevenStarsHierarchy = None

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RiskAssessment:
    """风险评估结果"""
    stock_code: str
    risk_level: RiskLevel
    risk_score: float
    risk_factors: List[str]
    recommendations: List[str]
    assessment_time: datetime
    confidence: float = 0.8

class RiskAssessmentService:
    """风险评估服务"""
    
    def __init__(self):
        self.service_name = "天玑星风险评估服务"
        self.version = "Professional v3.0"

        # 集成专业风险引擎
        try:
            from ..engines.professional_risk_engine import professional_risk_engine
            self.risk_engine = professional_risk_engine
            self.use_professional_engine = True
            logger.info("✅ 专业风险引擎已集成")
        except ImportError as e:
            logger.warning(f"专业风险引擎导入失败，使用基础引擎: {e}")
            self.risk_engine = None
            self.use_professional_engine = False

        # 风险评估权重 (专业级配置)
        self.risk_weights = {
            "market_risk": 0.25,      # 市场风险
            "liquidity_risk": 0.20,   # 流动性风险
            "volatility_risk": 0.25,  # 波动性风险
            "fundamental_risk": 0.20, # 基本面风险
            "technical_risk": 0.10    # 技术面风险
        }

        # 专业级风险阈值
        self.professional_thresholds = {
            "var_95_high": 0.05,      # VaR 95% 高风险阈值
            "var_99_critical": 0.08,  # VaR 99% 极高风险阈值
            "volatility_high": 0.30,  # 高波动率阈值
            "max_drawdown_high": 0.15, # 高回撤阈值
            "correlation_high": 0.80   # 高相关性阈值
        }
        
        # 风险阈值
        self.risk_thresholds = {
            "low": 0.3,
            "medium": 0.6,
            "high": 0.8
        }

        # 初始化四大核心系统
        self._init_core_systems()

        logger.info(f" {self.service_name} v{self.version} 初始化完成")

    def _init_core_systems(self):
        """初始化四大核心系统"""
        try:
            # 1. 传奇记忆系统
            if legendary_memory_interface:
                self.memory_system = legendary_memory_interface
            else:
                self.memory_system = None

            # 2. DeepSeek人设配置
            if get_memory_config:
                self.deepseek_memory_config = get_memory_config()
            else:
                self.deepseek_memory_config = None

            # 3. 绩效监控系统
            self.performance_monitor = performance_monitor

            # 4. 层级权限系统
            if EnhancedSevenStarsHierarchy:
                self.permission_system = EnhancedSevenStarsHierarchy()
            else:
                self.permission_system = None

            # 强制初始化所有核心系统
            if not self.memory_system:
                from core.domain.memory.legendary.interface import legendary_memory_interface
                self.memory_system = legendary_memory_interface

            if not self.deepseek_memory_config:
                from roles.tianji_star.config.deepseek_config import get_memory_config
                self.deepseek_memory_config = get_memory_config()

            logger.info("天玑星四大核心系统强制初始化完成")

        except Exception as e:
            logger.error(f"核心系统初始化失败: {e}")
            self.memory_system = None
            self.deepseek_memory_config = None
            self.performance_monitor = None
            self.permission_system = None

    async def _trigger_deepseek_memory(self, trigger_name: str, content: str, context: Dict[str, Any] = None):
        """根据DeepSeek配置触发记忆"""
        try:
            if not self.memory_system or not self.deepseek_memory_config:
                return

            # 获取触发器对应的记忆类型
            memory_type_mapping = {
                "high_risk_event": "risk_assessment_memory",
                "volatility_spike": "market_regime_memory",
                "correlation_breakdown": "stress_test_memory",
                "liquidity_crisis": "risk_model_memory"
            }

            memory_type = memory_type_mapping.get(trigger_name)
            if memory_type:
                # 根据记忆类型选择消息类型
                message_type_mapping = {
                    "risk_assessment_memory": MessageType.RISK_ASSESSMENT,
                    "market_regime_memory": MessageType.MARKET_ANALYSIS,
                    "stress_test_memory": MessageType.RISK_ASSESSMENT,
                    "risk_model_memory": MessageType.RISK_ASSESSMENT
                }

                message_type = message_type_mapping.get(memory_type, MessageType.GENERAL)

                # 添加到传奇记忆系统
                result = await self.memory_system.add_tianji_memory(
                    content=content,
                    message_type=message_type
                )

                if result.success:
                    logger.info(f"天玑星记忆触发成功: {trigger_name} -> {memory_type}")
                else:
                    logger.error(f"天玑星记忆触发失败: {result.message}")

        except Exception as e:
            logger.error(f"DeepSeek记忆触发失败: {e}")

    async def _record_performance_metric(self, metric_name: str, value: float, context: Dict[str, Any] = None):
        """记录绩效指标"""
        try:
            if self.performance_monitor:
                from performance.star_performance_monitor import PerformanceMetricType

                # 映射指标名称到枚举类型
                metric_type_mapping = {
                    "risk_assessment_accuracy": PerformanceMetricType.ACCURACY,
                    "risk_prediction_accuracy": PerformanceMetricType.ACCURACY,
                    "var_model_precision": PerformanceMetricType.QUALITY_SCORE,
                    "stress_test_coverage": PerformanceMetricType.SUCCESS_RATE
                }

                metric_type = metric_type_mapping.get(metric_name, PerformanceMetricType.ACCURACY)

                await self.performance_monitor.record_performance(
                    star_name="天玑星",
                    metric_type=metric_type,
                    value=value,
                    context=context or {}
                )
                logger.debug(f"天玑星绩效记录: {metric_name}={value}")
        except Exception as e:
            logger.error(f"绩效记录失败: {e}")
    
    async def assess_stock_risk(self, stock_code: str, position_size: float = 0, 
                               market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """评估股票风险"""
        try:
            assessment_time = datetime.now()
            
            # 获取市场数据
            market_data = await self._get_market_data(stock_code)
            
            # 计算各类风险
            market_risk = self._calculate_market_risk(market_data, market_context)
            liquidity_risk = self._calculate_liquidity_risk(market_data)
            volatility_risk = self._calculate_volatility_risk(market_data)
            fundamental_risk = self._calculate_fundamental_risk(market_data)
            technical_risk = self._calculate_technical_risk(market_data)

            # 使用专业风险引擎进行高级计算
            professional_metrics = {}
            if self.use_professional_engine and market_data.get('historical_data'):
                try:
                    professional_metrics = await self._calculate_professional_risk_metrics(market_data)

                    # 基于专业指标调整风险评分
                    if professional_metrics.get('var_95', 0) > self.professional_thresholds['var_95_high']:
                        volatility_risk = min(1.0, volatility_risk * 1.2)

                    if professional_metrics.get('max_drawdown', 0) > self.professional_thresholds['max_drawdown_high']:
                        market_risk = min(1.0, market_risk * 1.15)

                    logger.info(f"专业风险指标: VaR={professional_metrics.get('var_95', 0):.4f}")

                except Exception as e:
                    logger.warning(f"专业风险引擎计算失败: {e}")

            # 计算综合风险评分
            risk_score = (
                market_risk * self.risk_weights["market_risk"] +
                liquidity_risk * self.risk_weights["liquidity_risk"] +
                volatility_risk * self.risk_weights["volatility_risk"] +
                fundamental_risk * self.risk_weights["fundamental_risk"] +
                technical_risk * self.risk_weights["technical_risk"]
            )
            
            # 确定风险等级
            risk_level = self._determine_risk_level(risk_score)
            
            # 识别主要风险因子
            risk_factors = self._identify_risk_factors(
                market_risk, liquidity_risk, volatility_risk,
                fundamental_risk, technical_risk
            )
            
            # 生成风险建议
            recommendations = self._generate_risk_recommendations(
                risk_level, risk_factors, position_size
            )
            
            # 创建风险评估结果
            assessment = RiskAssessment(
                stock_code=stock_code,
                risk_level=risk_level,
                risk_score=risk_score,
                risk_factors=risk_factors,
                recommendations=recommendations,
                assessment_time=assessment_time,
                confidence=0.85
            )

            # 触发DeepSeek记忆和记录绩效
            await self._trigger_deepseek_memory(
                "high_risk_event" if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL] else "volatility_spike",
                f"风险评估: {stock_code} 风险等级{risk_level.value} 评分{risk_score:.3f}",
                {"stock_code": stock_code, "risk_level": risk_level.value}
            )

            # 记录绩效指标
            await self._record_performance_metric(
                "risk_assessment_accuracy",
                assessment.confidence,
                {"stock_code": stock_code, "risk_level": risk_level.value}
            )
            
            return {
                "success": True,
                "stock_code": stock_code,
                "risk_assessment": {
                    "risk_level": assessment.risk_level.value,
                    "risk_score": round(assessment.risk_score, 3),
                    "risk_factors": assessment.risk_factors,
                    "recommendations": assessment.recommendations,
                    "confidence": assessment.confidence,
                    "assessment_time": assessment.assessment_time.isoformat()
                },
                "detailed_risks": {
                    "market_risk": round(market_risk, 3),
                    "liquidity_risk": round(liquidity_risk, 3),
                    "volatility_risk": round(volatility_risk, 3),
                    "fundamental_risk": round(fundamental_risk, 3),
                    "technical_risk": round(technical_risk, 3)
                },
                "position_analysis": {
                    "position_size": position_size,
                    "position_risk": self._calculate_position_risk(risk_score, position_size),
                    "max_recommended_size": self._calculate_max_position_size(risk_score)
                },
                "professional_metrics": professional_metrics,
                "data_source": market_data.get("data_source", "unknown"),
                "engine_version": self.version
            }
            
        except Exception as e:
            logger.error(f"股票风险评估失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code,
                "timestamp": datetime.now().isoformat()
            }
    
    async def assess_portfolio_risk(self, portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """评估投资组合风险"""
        try:
            if not portfolio:
                return {
                    "success": False,
                    "error": "投资组合为空",
                    "timestamp": datetime.now().isoformat()
                }
            
            portfolio_risks = []
            total_value = sum(position.get('market_value', 0) for position in portfolio)
            
            # 评估每个持仓的风险
            for position in portfolio:
                stock_code = position.get('stock_code')
                position_size = position.get('market_value', 0)
                
                if stock_code:
                    risk_result = await self.assess_stock_risk(stock_code, position_size)
                    
                    if risk_result.get('success'):
                        weight = position_size / total_value if total_value > 0 else 0
                        risk_assessment = risk_result['risk_assessment']
                        
                        portfolio_risks.append({
                            'stock_code': stock_code,
                            'weight': weight,
                            'position_value': position_size,
                            'risk_score': risk_assessment['risk_score'],
                            'risk_level': risk_assessment['risk_level'],
                            'risk_factors': risk_assessment['risk_factors']
                        })
            
            # 计算组合风险
            portfolio_risk_score = sum(
                risk['weight'] * risk['risk_score'] 
                for risk in portfolio_risks
            )
            
            # 计算风险集中度
            risk_concentration = self._calculate_risk_concentration(portfolio_risks)
            
            # 计算相关性风险
            correlation_risk = self._calculate_correlation_risk(portfolio_risks)
            
            # 生成组合风险建议
            portfolio_recommendations = self._generate_portfolio_recommendations(
                portfolio_risk_score, risk_concentration, correlation_risk, portfolio_risks
            )
            
            return {
                "success": True,
                "portfolio_analysis": {
                    "total_value": total_value,
                    "position_count": len(portfolio_risks),
                    "portfolio_risk_score": round(portfolio_risk_score, 3),
                    "portfolio_risk_level": self._determine_risk_level(portfolio_risk_score).value,
                    "risk_concentration": round(risk_concentration, 3),
                    "correlation_risk": round(correlation_risk, 3)
                },
                "individual_risks": portfolio_risks,
                "recommendations": portfolio_recommendations,
                "risk_metrics": {
                    "diversification_score": 1 - risk_concentration,
                    "risk_adjusted_return": self._calculate_risk_adjusted_return(portfolio_risks),
                    "max_drawdown_estimate": self._estimate_max_drawdown(portfolio_risk_score)
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"投资组合风险评估失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _get_market_data(self, stock_code: str) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            # 优先使用股票数据库
            import sqlite3
            conn = sqlite3.connect("backend/data/stock_database.db")
            cursor = conn.cursor()

            # 处理股票代码格式 (去掉交易所后缀)
            clean_stock_code = stock_code.split('.')[0] if '.' in stock_code else stock_code

            # 获取基本信息
            cursor.execute("""
                SELECT stock_name, industry
                FROM stock_info
                WHERE stock_code = ?
            """, (clean_stock_code,))

            stock_info_data = cursor.fetchone()
            stock_info = {}
            if stock_info_data:
                stock_info = {
                    "name": stock_info_data[0],
                    "industry": stock_info_data[1]
                }

            # 获取历史价格数据
            cursor.execute("""
                SELECT trade_date, close_price, volume, turnover_rate, change_percent
                FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 60
            """, (clean_stock_code,))

            historical_data = []
            for row in cursor.fetchall():
                historical_data.append({
                    "date": row[0],
                    "close": row[1],
                    "volume": row[2],
                    "turnover_rate": row[3],
                    "change_percent": row[4]
                })

            # 获取财务数据 (从realtime_data表获取)
            cursor.execute("""
                SELECT pe_ratio, pb_ratio, market_cap
                FROM realtime_data
                WHERE stock_code = ?
            """, (clean_stock_code,))

            realtime_data = cursor.fetchone()
            fundamentals = {}
            if realtime_data:
                fundamentals = {
                    "pe_ratio": realtime_data[0],
                    "pb_ratio": realtime_data[1],
                    "market_cap": realtime_data[2]
                }

            conn.close()

            return {
                "current_price": historical_data[0]["close"] if historical_data else 0,
                "historical_data": historical_data,
                "stock_info": stock_info,
                "fundamentals": fundamentals,
                "data_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.warning(f"获取市场数据失败: {e}")
            # 备用数据源
            # 如果本地数据库没有数据，尝试从东方财富API获取
            return await self._get_eastmoney_market_data(stock_code)

    async def _get_eastmoney_market_data(self, stock_code: str) -> Dict[str, Any]:
        """从东方财富API获取市场数据"""
        try:
            # 使用东方财富API获取实时数据
            import requests

            # 东方财富股票基本信息API
            info_url = f"http://push2.eastmoney.com/api/qt/stock/get"
            info_params = {
                "secid": f"0.{stock_code}" if stock_code.startswith('0') or stock_code.startswith('3') else f"1.{stock_code}",
                "fields": "f57,f58,f162,f163,f164,f165"
            }

            info_response = requests.get(info_url, params=info_params, timeout=10)
            info_data = info_response.json()

            # 东方财富历史数据API
            hist_url = f"http://push2his.eastmoney.com/api/qt/stock/kline/get"
            hist_params = {
                "secid": f"0.{stock_code}" if stock_code.startswith('0') or stock_code.startswith('3') else f"1.{stock_code}",
                "klt": "101",  # 日K线
                "fqt": "1",    # 前复权
                "lmt": "60",   # 最近60天
                "fields1": "f1,f2,f3,f4,f5,f6",
                "fields2": "f51,f52,f53,f54,f55,f56,f57,f58"
            }

            hist_response = requests.get(hist_url, params=hist_params, timeout=10)
            hist_data = hist_response.json()

            # 解析数据
            current_price = 0
            historical_data = []

            if info_data.get("data") and info_data["data"].get("f162"):
                current_price = info_data["data"]["f162"]

            if hist_data.get("data") and hist_data["data"].get("klines"):
                for kline in hist_data["data"]["klines"]:
                    parts = kline.split(",")
                    if len(parts) >= 8:
                        historical_data.append({
                            "date": parts[0],
                            "close": float(parts[2]),
                            "volume": float(parts[5]),
                            "turnover_rate": float(parts[7]) if parts[7] != '-' else 0,
                            "change_percent": float(parts[8]) if len(parts) > 8 and parts[8] != '-' else 0
                        })

            # 计算基本面数据
            fundamentals = {}
            if info_data.get("data"):
                data = info_data["data"]
                fundamentals = {
                    "pe_ratio": data.get("f162"),
                    "pb_ratio": data.get("f163"),
                    "market_cap": data.get("f164")
                }

            return {
                "current_price": current_price,
                "historical_data": historical_data,
                "stock_info": {"name": f"股票{stock_code}", "industry": "未知"},
                "fundamentals": fundamentals,
                "data_timestamp": datetime.now().isoformat(),
                "data_source": "eastmoney_api"
            }

        except Exception as e:
            logger.error(f"东方财富API获取数据失败 {stock_code}: {e}")
            # 最后的保底方案：返回空数据而不是模拟数据
            return {
                "current_price": 0,
                "historical_data": [],
                "stock_info": {"name": f"股票{stock_code}", "industry": "未知"},
                "fundamentals": {},
                "data_timestamp": datetime.now().isoformat(),
                "data_source": "no_data_available",
                "error": str(e)
            }

    async def _calculate_professional_risk_metrics(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算专业风险指标"""
        try:
            if not self.use_professional_engine:
                return {}

            historical_data = market_data.get('historical_data', [])
            if len(historical_data) < 30:
                return {"error": "insufficient_data_for_professional_metrics"}

            # 提取收益率数据
            returns = []
            prices = []

            for i in range(1, len(historical_data)):
                if (historical_data[i-1].get('close') and
                    historical_data[i].get('close')):
                    prev_price = float(historical_data[i-1]['close'])
                    curr_price = float(historical_data[i]['close'])

                    if prev_price > 0:
                        return_rate = (curr_price - prev_price) / prev_price
                        returns.append(return_rate)
                        prices.append(curr_price)

            if len(returns) < 30:
                return {"error": "insufficient_returns_data"}

            # 计算专业风险指标
            professional_metrics = {}

            # VaR计算
            var_95 = self.risk_engine.calculate_professional_var(returns, 0.95, "historical")
            var_99 = self.risk_engine.calculate_professional_var(returns, 0.99, "historical")

            # CVaR计算
            cvar_95 = self.risk_engine.calculate_conditional_var(returns, 0.95)

            # 最大回撤计算
            if len(prices) >= 30:
                drawdown_result = self.risk_engine.calculate_maximum_drawdown(prices)
                professional_metrics['max_drawdown'] = drawdown_result.get('max_drawdown', 0)
                professional_metrics['current_drawdown'] = drawdown_result.get('current_drawdown', 0)

            # GARCH波动率预测
            garch_result = self.risk_engine.calculate_garch_volatility(returns)

            # 综合风险指标
            portfolio_metrics = self.risk_engine.calculate_portfolio_risk_metrics(returns)

            professional_metrics.update({
                'var_95': var_95.get('var', 0),
                'var_99': var_99.get('var', 0),
                'cvar_95': cvar_95.get('cvar', 0),
                'garch_volatility': garch_result.get('current_volatility', 0),
                'annualized_volatility': garch_result.get('annualized_volatility', 0),
                'sharpe_ratio': portfolio_metrics.get('sharpe_ratio', 0),
                'skewness': portfolio_metrics.get('skewness', 0),
                'kurtosis': portfolio_metrics.get('kurtosis', 0),
                'data_quality': 'high' if len(returns) >= 60 else 'medium',
                'calculation_method': 'professional_engine_v3'
            })

            return professional_metrics

        except Exception as e:
            logger.error(f"专业风险指标计算失败: {e}")
            return {"error": str(e)}

    def _calculate_market_risk(self, market_data: Dict[str, Any], market_context: Dict[str, Any] = None) -> float:
        """计算市场风险"""
        try:
            # 基础市场风险评估
            base_risk = 0.5
            
            # 考虑市场环境
            if market_context:
                market_trend = market_context.get("market_trend", "neutral")
                if market_trend == "bearish":
                    base_risk += 0.2
                elif market_trend == "bullish":
                    base_risk -= 0.1
            
            # 考虑股票类型
            stock_info = market_data.get("stock_info", {})
            if stock_info:
                stock_type = stock_info.get("type", "")
                if "ST" in stock_type:
                    base_risk += 0.3
                elif "创业板" in stock_type:
                    base_risk += 0.1
            
            return min(max(base_risk, 0.0), 1.0)
            
        except Exception as e:
            logger.warning(f"计算市场风险失败: {e}")
            return 0.5
    
    def _calculate_liquidity_risk(self, market_data: Dict[str, Any]) -> float:
        """计算流动性风险"""
        try:
            historical_data = market_data.get("historical_data", [])
            if not historical_data:
                return 0.8
            
            # 计算平均成交量
            volumes = [float(data.get("volume", 0)) for data in historical_data[-10:]]
            avg_volume = sum(volumes) / len(volumes) if volumes else 0
            
            # 流动性风险评估
            if avg_volume > 10000000:  # 1000万股
                return 0.2
            elif avg_volume > 5000000:  # 500万股
                return 0.4
            elif avg_volume > 1000000:  # 100万股
                return 0.6
            else:
                return 0.8
                
        except Exception as e:
            logger.warning(f"计算流动性风险失败: {e}")
            return 0.5
    
    def _calculate_volatility_risk(self, market_data: Dict[str, Any]) -> float:
        """计算波动性风险"""
        try:
            historical_data = market_data.get("historical_data", [])
            if len(historical_data) < 10:
                return 0.6
            
            # 计算价格波动率
            prices = [float(data["close"]) for data in historical_data]
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
            
            if len(returns) > 1:
                import statistics
                volatility = statistics.stdev(returns)
                
                # 波动率风险评估
                if volatility > 0.05:  # 5%日波动率
                    return 0.9
                elif volatility > 0.03:  # 3%日波动率
                    return 0.7
                elif volatility > 0.02:  # 2%日波动率
                    return 0.5
                else:
                    return 0.3
            else:
                return 0.5
                
        except Exception as e:
            logger.warning(f"计算波动性风险失败: {e}")
            return 0.5
    
    def _calculate_fundamental_risk(self, market_data: Dict[str, Any]) -> float:
        """计算基本面风险"""
        try:
            fundamentals = market_data.get("fundamentals", {})
            if not fundamentals:
                return 0.6
            
            pe_ratio = fundamentals.get("pe_ratio", 0)
            pb_ratio = fundamentals.get("pb_ratio", 0)
            
            risk_score = 0.5
            
            # PE比率风险
            if pe_ratio > 50:
                risk_score += 0.2
            elif pe_ratio < 0:  # 负PE
                risk_score += 0.3
            elif pe_ratio < 10:
                risk_score -= 0.1
            
            # PB比率风险
            if pb_ratio > 5:
                risk_score += 0.2
            elif pb_ratio < 1:
                risk_score -= 0.1
            
            return min(max(risk_score, 0.0), 1.0)
            
        except Exception as e:
            logger.warning(f"计算基本面风险失败: {e}")
            return 0.5
    
    def _calculate_technical_risk(self, market_data: Dict[str, Any]) -> float:
        """计算技术面风险"""
        try:
            historical_data = market_data.get("historical_data", [])
            if len(historical_data) < 5:
                return 0.5
            
            # 简单的技术面风险评估
            recent_data = historical_data[-5:]
            
            # 检查连续下跌
            consecutive_down = 0
            for i in range(1, len(recent_data)):
                if float(recent_data[i]["close"]) < float(recent_data[i-1]["close"]):
                    consecutive_down += 1
                else:
                    break
            
            # 技术面风险评估
            if consecutive_down >= 4:
                return 0.8
            elif consecutive_down >= 2:
                return 0.6
            else:
                return 0.4
                
        except Exception as e:
            logger.warning(f"计算技术面风险失败: {e}")
            return 0.5
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """确定风险等级"""
        if risk_score >= self.risk_thresholds["high"]:
            return RiskLevel.CRITICAL
        elif risk_score >= self.risk_thresholds["medium"]:
            return RiskLevel.HIGH
        elif risk_score >= self.risk_thresholds["low"]:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _identify_risk_factors(self, market_risk: float, liquidity_risk: float,
                             volatility_risk: float, fundamental_risk: float,
                             technical_risk: float) -> List[str]:
        """识别主要风险因子"""
        risk_factors = []
        
        if market_risk > 0.6:
            risk_factors.append("市场环境风险")
        if liquidity_risk > 0.6:
            risk_factors.append("流动性不足风险")
        if volatility_risk > 0.6:
            risk_factors.append("价格波动风险")
        if fundamental_risk > 0.6:
            risk_factors.append("基本面风险")
        if technical_risk > 0.6:
            risk_factors.append("技术面风险")
        
        return risk_factors if risk_factors else ["风险可控"]
    
    def _generate_risk_recommendations(self, risk_level: RiskLevel, 
                                     risk_factors: List[str], 
                                     position_size: float) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        if risk_level == RiskLevel.CRITICAL:
            recommendations.extend([
                "立即停止交易或大幅减仓",
                "等待风险因素消除",
                "加强风险监控"
            ])
        elif risk_level == RiskLevel.HIGH:
            recommendations.extend([
                "谨慎交易，控制仓位",
                "设置严格止损",
                "密切关注风险变化"
            ])
        elif risk_level == RiskLevel.MEDIUM:
            recommendations.extend([
                "适度控制仓位",
                "设置合理止损",
                "定期评估风险"
            ])
        else:
            recommendations.extend([
                "可正常交易",
                "保持风险监控",
                "适当仓位管理"
            ])
        
        # 基于具体风险因子的建议
        if "流动性不足风险" in risk_factors:
            recommendations.append("注意流动性，避免大额交易")
        if "价格波动风险" in risk_factors:
            recommendations.append("控制单笔交易规模")
        
        return recommendations
    
    def _calculate_position_risk(self, risk_score: float, position_size: float) -> float:
        """计算仓位风险"""
        return risk_score * (position_size / 1000000)  # 假设100万为基准
    
    def _calculate_max_position_size(self, risk_score: float) -> float:
        """计算最大建议仓位"""
        if risk_score > 0.8:
            return 50000  # 5万
        elif risk_score > 0.6:
            return 100000  # 10万
        elif risk_score > 0.4:
            return 200000  # 20万
        else:
            return 500000  # 50万
    
    def _calculate_risk_concentration(self, portfolio_risks: List[Dict]) -> float:
        """计算风险集中度"""
        try:
            if not portfolio_risks:
                return 0.0
            
            # 计算高风险股票的权重集中度
            high_risk_weight = sum(
                risk['weight'] for risk in portfolio_risks 
                if risk['risk_score'] > 0.6
            )
            
            return high_risk_weight
            
        except Exception as e:
            logger.warning(f"计算风险集中度失败: {e}")
            return 0.5
    
    def _calculate_correlation_risk(self, portfolio_risks: List[Dict]) -> float:
        """计算相关性风险"""
        # 简化的相关性风险评估
        # 实际应该计算股票间的相关系数
        return 0.3  # 假设中等相关性风险
    
    def _generate_portfolio_recommendations(self, portfolio_risk_score: float,
                                          risk_concentration: float,
                                          correlation_risk: float,
                                          portfolio_risks: List[Dict]) -> List[str]:
        """生成投资组合风险建议"""
        recommendations = []
        
        if portfolio_risk_score > 0.7:
            recommendations.append("投资组合整体风险偏高，建议降低仓位")
        
        if risk_concentration > 0.5:
            recommendations.append("高风险股票集中度过高，建议分散投资")
        
        if correlation_risk > 0.6:
            recommendations.append("投资组合相关性过高，建议增加多样化")
        
        # 针对具体高风险股票的建议
        high_risk_stocks = [
            risk['stock_code'] for risk in portfolio_risks 
            if risk['risk_score'] > 0.7
        ]
        
        if high_risk_stocks:
            recommendations.append(f"建议减持高风险股票: {', '.join(high_risk_stocks[:3])}")
        
        if not recommendations:
            recommendations.append("投资组合风险控制良好，继续保持")
        
        return recommendations
    
    def _calculate_risk_adjusted_return(self, portfolio_risks: List[Dict]) -> float:
        """计算风险调整收益"""
        # 简化的风险调整收益计算
        avg_risk = sum(risk['risk_score'] for risk in portfolio_risks) / len(portfolio_risks) if portfolio_risks else 0.5
        return max(0.1 - avg_risk * 0.1, 0.01)  # 假设基础收益10%，风险越高收益越低
    
    def _estimate_max_drawdown(self, portfolio_risk_score: float) -> float:
        """估算最大回撤"""
        # 基于风险评分估算最大回撤
        return min(portfolio_risk_score * 0.3, 0.25)  # 最大25%回撤

# 全局风险评估服务实例
risk_assessment_service = RiskAssessmentService()

__all__ = ["risk_assessment_service", "RiskAssessmentService", "RiskAssessment", "RiskLevel"]
