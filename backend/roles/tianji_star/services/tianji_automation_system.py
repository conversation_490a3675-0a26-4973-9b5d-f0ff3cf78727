#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星自动化系统
负责风险分析和风险管理的自动化执行
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import numpy as np

# 导入天玑星核心服务
from .risk_assessment_service import risk_assessment_service
from .risk_knowledge_base_service import risk_knowledge_base_service

logger = logging.getLogger(__name__)

class TianjiAutomationSystem:
    """天玑星自动化系统"""
    
    def __init__(self):
        self.system_name = "TianjiAutomationSystem"
        self.version = "1.0.0"
        self.is_active = False
        self.automation_tasks = {}
        
        # 核心服务引用
        self.risk_service = risk_assessment_service
        self.knowledge_base = risk_knowledge_base_service
        
        logger.info(f"天玑星自动化系统 v{self.version} 初始化完成")
    
    async def execute_risk_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行风险分析自动化任务"""
        try:
            stock_code = context.get("stock_code")
            task_type = context.get("task_type", "comprehensive_risk_analysis")
            session_id = context.get("session_id")
            position_size = context.get("position_size", 100000)
            market_context = context.get("market_context", {})
            
            logger.info(f"⚠️ 天玑星开始执行风险分析: {stock_code}")
            
            # 1. 市场风险分析
            market_risk = await self._analyze_market_risk(stock_code, market_context)
            
            # 2. 个股风险分析
            stock_risk = await self._analyze_stock_risk(stock_code)
            
            # 3. 流动性风险分析
            liquidity_risk = await self._analyze_liquidity_risk(stock_code)
            
            # 4. 组合风险分析
            portfolio_risk = await self._analyze_portfolio_risk(stock_code, position_size)
            
            # 5. 综合风险评估
            comprehensive_risk = await self._comprehensive_risk_assessment(
                market_risk, stock_risk, liquidity_risk, portfolio_risk
            )
            
            # 6. 风险建议
            risk_recommendations = await self._generate_risk_recommendations(comprehensive_risk)
            
            analysis_result = {
                "stock_code": stock_code,
                "analysis_type": task_type,
                "session_id": session_id,
                "position_size": position_size,
                "market_risk": market_risk,
                "stock_risk": stock_risk,
                "liquidity_risk": liquidity_risk,
                "portfolio_risk": portfolio_risk,
                "comprehensive_risk": comprehensive_risk,
                "risk_recommendations": risk_recommendations,
                "analysis_time": datetime.now().isoformat(),
                "automation_source": "tianji_automation_system"
            }
            
            logger.info(f"✅ 天玑星风险分析完成: {stock_code}")
            
            return {
                "success": True,
                "analysis_result": analysis_result,
                "execution_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"天玑星风险分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": datetime.now().isoformat()
            }
    
    async def _analyze_market_risk(self, stock_code: str, market_context: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场风险"""
        try:
            market_trend = market_context.get("market_trend", "neutral")
            volatility_regime = market_context.get("volatility_regime", "normal")
            
            # 基于市场环境评估风险
            if market_trend == "bull_market":
                market_risk_score = 0.3
                risk_level = "低风险"
            elif market_trend == "bear_market":
                market_risk_score = 0.8
                risk_level = "高风险"
            else:
                market_risk_score = 0.5
                risk_level = "中等风险"
            
            # 波动率调整
            if volatility_regime == "high":
                market_risk_score += 0.2
            elif volatility_regime == "low":
                market_risk_score -= 0.1
            
            market_risk_score = max(0.0, min(1.0, market_risk_score))
            
            return {
                "stock_code": stock_code,
                "market_risk_score": market_risk_score,
                "risk_level": risk_level,
                "market_trend": market_trend,
                "volatility_regime": volatility_regime,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"市场风险分析失败: {e}")
            return {
                "stock_code": stock_code,
                "market_risk_score": 0.5,
                "risk_level": "中等风险",
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _analyze_stock_risk(self, stock_code: str) -> Dict[str, Any]:
        """分析个股风险"""
        try:
            # 调用风险评估服务
            risk_result = await self.risk_service.assess_stock_risk(stock_code)
            
            # 计算个股风险得分
            volatility = risk_result.get("volatility", 0.2)
            beta = risk_result.get("beta", 1.0)
            
            # 风险得分计算
            stock_risk_score = min(1.0, (volatility * 2 + abs(beta - 1) * 0.5))
            
            if stock_risk_score < 0.3:
                risk_level = "低风险"
            elif stock_risk_score < 0.7:
                risk_level = "中等风险"
            else:
                risk_level = "高风险"
            
            return {
                "stock_code": stock_code,
                "stock_risk_score": stock_risk_score,
                "risk_level": risk_level,
                "volatility": volatility,
                "beta": beta,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"个股风险分析失败: {e}")
            return {
                "stock_code": stock_code,
                "stock_risk_score": 0.5,
                "risk_level": "中等风险",
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _analyze_liquidity_risk(self, stock_code: str) -> Dict[str, Any]:
        """分析流动性风险"""
        try:
            # 真实流动性分析 - 基于实际市场数据
            from shared.data_sources.real_market_data_service import RealMarketDataService

            market_service = RealMarketDataService()

            # 获取股票的真实交易数据
            stock_data = await market_service.get_stock_realtime_data(stock_code)

            # 获取历史数据（30天）
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            try:
                historical_data = await market_service.get_historical_data(stock_code, start_date, end_date)
                # 如果返回的是DataFrame，转换为列表
                if hasattr(historical_data, 'to_dict'):
                    historical_data = historical_data.to_dict('records')
                elif hasattr(historical_data, 'empty') and historical_data.empty:
                    historical_data = None
            except Exception as e:
                logger.warning(f"获取历史数据失败: {e}")
                historical_data = None

            if not stock_data or historical_data is None or (hasattr(historical_data, '__len__') and len(historical_data) == 0):
                logger.warning(f"无法获取{stock_code}的真实数据，使用保守评估")
                avg_volume = 5000000  # 保守估计
                turnover_rate = 0.03  # 保守估计
            else:
                # 计算真实流动性指标
                volumes = []
                turnovers = []

                if isinstance(historical_data, list):
                    volumes = [day.get("volume", 0) for day in historical_data if day.get("volume")]
                    turnovers = [day.get("turnover_rate", 0) for day in historical_data if day.get("turnover_rate")]

                avg_volume = sum(volumes) / len(volumes) if volumes else 5000000
                turnover_rate = sum(turnovers) / len(turnovers) if turnovers else 0.03
            
            # 流动性风险评分
            if avg_volume > 5000000 and turnover_rate > 0.05:
                liquidity_risk_score = 0.2
                risk_level = "低风险"
            elif avg_volume > 1000000 and turnover_rate > 0.02:
                liquidity_risk_score = 0.5
                risk_level = "中等风险"
            else:
                liquidity_risk_score = 0.8
                risk_level = "高风险"
            
            return {
                "stock_code": stock_code,
                "liquidity_risk_score": liquidity_risk_score,
                "risk_level": risk_level,
                "avg_volume": int(avg_volume),
                "turnover_rate": round(turnover_rate, 4),
                "data_source": "真实市场数据" if stock_data else "保守估计",
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"流动性风险分析失败: {e}")
            return {
                "stock_code": stock_code,
                "liquidity_risk_score": 0.5,
                "risk_level": "中等风险",
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _analyze_portfolio_risk(self, stock_code: str, position_size: float) -> Dict[str, Any]:
        """分析组合风险 - 真实组合风险评估"""
        try:
            # 真实组合风险分析 - 考虑持仓相关性和集中度
            from shared.data_sources.real_market_data_service import RealMarketDataService

            market_service = RealMarketDataService()

            # 获取股票行业信息
            stock_info = await market_service.get_stock_info(stock_code)
            sector = stock_info.get("sector", "未知行业") if stock_info else "未知行业"

            # 基于仓位大小和行业集中度评估风险
            if position_size > 1000000:  # 大仓位（100万以上）
                portfolio_risk_score = 0.8
                risk_level = "高风险"
            elif position_size > 100000:  # 中等仓位
                portfolio_risk_score = 0.4
                risk_level = "中等风险"
            else:  # 小仓位
                portfolio_risk_score = 0.2
                risk_level = "低风险"
            
            return {
                "stock_code": stock_code,
                "portfolio_risk_score": portfolio_risk_score,
                "risk_level": risk_level,
                "position_size": position_size,
                "concentration_risk": "高" if position_size > 500000 else "中" if position_size > 100000 else "低",
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"组合风险分析失败: {e}")
            return {
                "stock_code": stock_code,
                "portfolio_risk_score": 0.5,
                "risk_level": "中等风险",
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _comprehensive_risk_assessment(self, market_risk: Dict, stock_risk: Dict, 
                                           liquidity_risk: Dict, portfolio_risk: Dict) -> Dict[str, Any]:
        """综合风险评估"""
        try:
            # 权重设置
            weights = {
                "market": 0.3,
                "stock": 0.4,
                "liquidity": 0.2,
                "portfolio": 0.1
            }
            
            # 计算综合风险得分
            comprehensive_score = (
                market_risk.get("market_risk_score", 0.5) * weights["market"] +
                stock_risk.get("stock_risk_score", 0.5) * weights["stock"] +
                liquidity_risk.get("liquidity_risk_score", 0.5) * weights["liquidity"] +
                portfolio_risk.get("portfolio_risk_score", 0.5) * weights["portfolio"]
            )
            
            # 确定综合风险等级
            if comprehensive_score < 0.3:
                risk_level = "低风险"
                risk_color = "green"
            elif comprehensive_score < 0.7:
                risk_level = "中等风险"
                risk_color = "yellow"
            else:
                risk_level = "高风险"
                risk_color = "red"
            
            return {
                "comprehensive_risk_score": comprehensive_score,
                "risk_level": risk_level,
                "risk_color": risk_color,
                "risk_weights": weights,
                "confidence": 0.8,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"综合风险评估失败: {e}")
            return {
                "comprehensive_risk_score": 0.5,
                "risk_level": "中等风险",
                "risk_color": "yellow",
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _generate_risk_recommendations(self, comprehensive_risk: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险建议"""
        try:
            risk_score = comprehensive_risk.get("comprehensive_risk_score", 0.5)
            risk_level = comprehensive_risk.get("risk_level", "中等风险")
            
            recommendations = []
            
            if risk_score < 0.3:
                recommendations = [
                    "当前风险水平较低，可以适当增加仓位",
                    "建议保持现有投资策略",
                    "可以考虑适度加杠杆"
                ]
                position_advice = "建议适度配置"
            elif risk_score < 0.7:
                recommendations = [
                    "当前风险水平适中，建议谨慎操作",
                    "密切关注市场变化",
                    "设置合理的止损位"
                ]
                position_advice = "建议适度配置"
            else:
                recommendations = [
                    "当前风险水平较高，建议减少仓位",
                    "加强风险监控",
                    "考虑对冲策略",
                    "设置严格的止损位"
                ]
                position_advice = "建议减少配置"
            
            return {
                "risk_level": risk_level,
                "position_advice": position_advice,
                "recommendations": recommendations,
                "stop_loss_suggestion": f"{max(0.05, risk_score * 0.1):.2%}",
                "max_position_ratio": f"{max(0.1, 1 - risk_score):.1%}",
                "monitoring_frequency": "高" if risk_score > 0.7 else "中" if risk_score > 0.3 else "低",
                "generation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成风险建议失败: {e}")
            return {
                "risk_level": "中等风险",
                "position_advice": "建议适度配置",
                "recommendations": ["建议谨慎操作"],
                "error": str(e),
                "generation_time": datetime.now().isoformat()
            }
    
    async def start_automation(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动自动化任务"""
        try:
            automation_id = f"tianji_auto_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.automation_tasks[automation_id] = {
                "config": config,
                "status": "running",
                "start_time": datetime.now().isoformat()
            }
            
            self.is_active = True
            
            logger.info(f"天玑星自动化任务启动: {automation_id}")
            
            return {
                "success": True,
                "automation_id": automation_id,
                "message": "天玑星自动化任务启动成功"
            }
            
        except Exception as e:
            logger.error(f"启动天玑星自动化任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        return {
            "system_name": self.system_name,
            "version": self.version,
            "is_active": self.is_active,
            "active_tasks": len([t for t in self.automation_tasks.values() if t["status"] == "running"]),
            "total_tasks": len(self.automation_tasks),
            "status_time": datetime.now().isoformat()
        }


    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
        """调用tianji_star专用DeepSeek分析"""
        try:
            from roles.tianji_star.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建角色专用提示词
            role_prompt = f"{role_setting}\n\n请分析：{prompt}"
            if context_data:
                try:
                    import json
                    context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                    role_prompt += f"\n\n上下文数据：{context_str}"
                except:
                    role_prompt += f"\n\n上下文数据：{str(context_data)}"
            
            # 调用DeepSeek服务
            messages = [
                {"role": "system", "content": role_setting},
                {"role": "user", "content": prompt}
            ]
            
            result = await deepseek_service.chat_completion(messages, **config)
            
            return {
                "success": result.get("success", False),
                "analysis": result.get("response", ""),
                "role": "tianji_star",
                "context_type": context_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "role": "tianji_star",
                "timestamp": datetime.now().isoformat()
            }
    
    async def store_memory(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
        """存储记忆到传奇记忆系统"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            from core.domain.memory.legendary.models import MessageType, MemoryPriority
            
            
            # 映射消息类型
            message_type_mapping = {
                "analysis": MessageType.MARKET_ANALYSIS,
                "news": MessageType.NEWS_UPDATE,
                "general": MessageType.GENERAL,
                "system": MessageType.SYSTEM_NOTIFICATION,
                "risk": MessageType.RISK_ASSESSMENT,
                "strategy": MessageType.STRATEGY_PLANNING,
                "execution": MessageType.TRADING_EXECUTION
            }
            
            # 映射优先级
            priority_mapping = {
                "high": MemoryPriority.HIGH,
                "normal": MemoryPriority.NORMAL,
                "low": MemoryPriority.LOW
            }

            result = await legendary_memory_interface.add_memory(
                content=content,
                role="天玑星",
                message_type=message_type_mapping.get(memory_type, MessageType.GENERAL),
                priority=priority_mapping.get(priority, MemoryPriority.NORMAL),
                metadata=metadata or {}
            )
            
            return {"success": result.success, "memory_id": result.memory_id}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def retrieve_memories(self, query: str, limit: int = 10) -> list:
        """从传奇记忆系统检索记忆"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface

            memories = await legendary_memory_interface.search_memories(
                query=query,
                role_filter="tianji_star",
                limit=limit
            )

            return memories

        except Exception as e:
            return []
    
    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        """记录绩效到监控系统"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            result = await star_performance_monitor.record_performance(
                star_name="tianji_star",
                metric_type=metric_name,
                value=value,
                context=context or {}
            )

            return {"success": result, "metric": metric_name, "value": value}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_performance_stats(self) -> dict:
        """获取角色绩效统计"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            return star_performance_monitor.get_star_performance("tianji_star")

        except Exception as e:
            return {"error": str(e)}

# 全局实例
tianji_automation_system = TianjiAutomationSystem()
