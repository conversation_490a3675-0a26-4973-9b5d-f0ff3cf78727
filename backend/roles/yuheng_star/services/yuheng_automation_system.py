#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玉衡星自动化系统
负责交易执行和订单管理的自动化执行
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import uuid

# 导入玉衡星核心服务
from .trading_execution_service import trading_execution_service
from .virtual_trading_service import virtual_trading_service
from .order_management_service import order_management_service

logger = logging.getLogger(__name__)

class YuhengAutomationSystem:
    """玉衡星自动化系统"""
    
    def __init__(self):
        self.system_name = "YuhengAutomationSystem"
        self.version = "1.0.0"
        self.is_active = False
        self.automation_tasks = {}
        
        # 核心服务引用
        self.trading_service = trading_execution_service
        self.virtual_trading = virtual_trading_service
        self.order_service = order_management_service
        
        # 交易统计
        self.trading_stats = {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "total_profit_loss": 0.0
        }
        
        logger.info(f"玉衡星自动化系统 v{self.version} 初始化完成")
    
    async def execute_trading_automation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易自动化任务"""
        try:
            stock_code = context.get("stock_code")
            task_type = context.get("task_type", "learning_trading")
            session_id = context.get("session_id")
            trading_decision = context.get("trading_decision", {})
            mode = context.get("mode", "learning")  # learning 或 live
            
            logger.info(f"💰 玉衡星开始执行交易自动化: {stock_code} ({mode}模式)")
            
            if mode == "learning":
                # 学习模式交易
                trading_result = await self._execute_learning_trading(
                    stock_code, trading_decision, session_id
                )
            else:
                # 实盘模式交易
                trading_result = await self._execute_live_trading(
                    stock_code, trading_decision, session_id
                )
            
            # 更新交易统计
            await self._update_trading_stats(trading_result)
            
            # 生成交易报告
            trading_report = await self._generate_trading_report(trading_result)
            
            automation_result = {
                "stock_code": stock_code,
                "task_type": task_type,
                "session_id": session_id,
                "mode": mode,
                "trading_decision": trading_decision,
                "trading_result": trading_result,
                "trading_report": trading_report,
                "execution_time": datetime.now().isoformat(),
                "automation_source": "yuheng_automation_system"
            }
            
            logger.info(f"✅ 玉衡星交易自动化完成: {stock_code}")
            
            return {
                "success": True,
                "automation_result": automation_result,
                "execution_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"玉衡星交易自动化失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": datetime.now().isoformat()
            }
    
    async def _execute_learning_trading(self, stock_code: str, trading_decision: Dict[str, Any],
                                      session_id: str) -> Dict[str, Any]:
        """执行学习模式交易"""
        try:
            # 解析交易决策
            action = trading_decision.get("action", "hold")
            quantity = trading_decision.get("quantity", 1000)
            price = trading_decision.get("price", 10.0)
            
            # 生成交易订单
            order_id = str(uuid.uuid4())
            
            # 模拟交易执行
            if action in ["buy", "sell"]:
                # 调用虚拟交易服务（修复参数传递）
                execution_result = await self.virtual_trading.execute_virtual_trade(
                    stock_code=stock_code,
                    action=action,
                    quantity=quantity,
                    strategy_name=f"learning_{session_id}"
                )
                
                # 计算盈亏
                if action == "buy":
                    cost = quantity * price
                    profit_loss = 0.0  # 买入时无盈亏
                else:  # sell
                    cost = -(quantity * price)
                    profit_loss = quantity * price * 0.01  # 模拟1%收益
                
                trade_result = {
                    "order_id": order_id,
                    "stock_code": stock_code,
                    "action": action,
                    "quantity": quantity,
                    "price": price,
                    "cost": cost,
                    "profit_loss": profit_loss,
                    "status": "executed",
                    "execution_time": datetime.now().isoformat(),
                    "mode": "learning"
                }
                
            else:  # hold
                trade_result = {
                    "order_id": order_id,
                    "stock_code": stock_code,
                    "action": "hold",
                    "quantity": 0,
                    "price": price,
                    "cost": 0.0,
                    "profit_loss": 0.0,
                    "status": "hold",
                    "execution_time": datetime.now().isoformat(),
                    "mode": "learning"
                }
            
            return {
                "success": True,
                "trade_result": trade_result,
                "execution_method": "virtual_trading"
            }
            
        except Exception as e:
            logger.error(f"学习模式交易执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_method": "virtual_trading"
            }
    
    async def _execute_live_trading(self, stock_code: str, trading_decision: Dict[str, Any],
                                  session_id: str) -> Dict[str, Any]:
        """执行实盘模式交易"""
        try:
            # 解析交易决策
            action = trading_decision.get("action", "hold")
            quantity = trading_decision.get("quantity", 100)
            price = trading_decision.get("price", 10.0)
            
            # 生成交易订单
            order_id = str(uuid.uuid4())
            
            # 风险检查
            risk_check = await self._perform_risk_check(stock_code, action, quantity, price)
            
            if not risk_check["passed"]:
                return {
                    "success": False,
                    "error": f"风险检查未通过: {risk_check['reason']}",
                    "execution_method": "live_trading"
                }
            
            # 实盘交易执行（这里使用模拟，实际应该连接真实交易接口）
            if action in ["buy", "sell"]:
                # 调用交易执行服务
                execution_result = await self.trading_service.execute_trade({
                    "order_id": order_id,
                    "stock_code": stock_code,
                    "action": action,
                    "quantity": quantity,
                    "price": price,
                    "session_id": session_id,
                    "mode": "live"
                })
                
                # 实际交易结果
                trade_result = {
                    "order_id": order_id,
                    "stock_code": stock_code,
                    "action": action,
                    "quantity": quantity,
                    "price": price,
                    "cost": quantity * price if action == "buy" else -(quantity * price),
                    "profit_loss": 0.0,  # 实盘交易的盈亏需要后续计算
                    "status": "executed",
                    "execution_time": datetime.now().isoformat(),
                    "mode": "live",
                    "commission": quantity * price * 0.0003,  # 手续费
                    "risk_check": risk_check
                }
                
            else:  # hold
                trade_result = {
                    "order_id": order_id,
                    "stock_code": stock_code,
                    "action": "hold",
                    "quantity": 0,
                    "price": price,
                    "cost": 0.0,
                    "profit_loss": 0.0,
                    "status": "hold",
                    "execution_time": datetime.now().isoformat(),
                    "mode": "live"
                }
            
            return {
                "success": True,
                "trade_result": trade_result,
                "execution_method": "live_trading"
            }
            
        except Exception as e:
            logger.error(f"实盘模式交易执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_method": "live_trading"
            }
    
    async def _perform_risk_check(self, stock_code: str, action: str, quantity: int, price: float) -> Dict[str, Any]:
        """执行风险检查"""
        try:
            # 基本风险检查
            checks = []
            
            # 1. 价格合理性检查
            if price <= 0:
                return {"passed": False, "reason": "价格不能为零或负数"}
            
            if price > 1000:
                return {"passed": False, "reason": "价格过高，可能存在错误"}
            
            # 2. 数量合理性检查
            if quantity <= 0:
                return {"passed": False, "reason": "交易数量必须大于零"}
            
            if quantity > 1000000:
                return {"passed": False, "reason": "单笔交易数量过大"}
            
            # 3. 资金检查（模拟）
            if action == "buy":
                required_funds = quantity * price
                if required_funds > 1000000:  # 假设最大可用资金100万
                    return {"passed": False, "reason": "资金不足"}
            
            # 4. 持仓检查（模拟）
            if action == "sell":
                # 这里应该检查实际持仓，暂时跳过
                pass
            
            return {
                "passed": True,
                "reason": "风险检查通过",
                "checks_performed": ["price_check", "quantity_check", "funds_check", "position_check"]
            }
            
        except Exception as e:
            logger.error(f"风险检查失败: {e}")
            return {
                "passed": False,
                "reason": f"风险检查异常: {str(e)}"
            }
    
    async def _update_trading_stats(self, trading_result: Dict[str, Any]) -> None:
        """更新交易统计"""
        try:
            if trading_result.get("success"):
                trade_result = trading_result.get("trade_result", {})
                
                self.trading_stats["total_trades"] += 1
                
                if trade_result.get("status") == "executed":
                    self.trading_stats["successful_trades"] += 1
                    profit_loss = trade_result.get("profit_loss", 0.0)
                    self.trading_stats["total_profit_loss"] += profit_loss
                else:
                    self.trading_stats["failed_trades"] += 1
            else:
                self.trading_stats["total_trades"] += 1
                self.trading_stats["failed_trades"] += 1
                
        except Exception as e:
            logger.error(f"更新交易统计失败: {e}")
    
    async def _generate_trading_report(self, trading_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易报告"""
        try:
            if not trading_result.get("success"):
                return {
                    "report_type": "failed_trade",
                    "error": trading_result.get("error"),
                    "generation_time": datetime.now().isoformat()
                }
            
            trade_result = trading_result.get("trade_result", {})
            
            # 计算成功率
            total_trades = self.trading_stats["total_trades"]
            success_rate = (self.trading_stats["successful_trades"] / total_trades * 100) if total_trades > 0 else 0
            
            report = {
                "report_type": "trading_summary",
                "trade_details": {
                    "stock_code": trade_result.get("stock_code"),
                    "action": trade_result.get("action"),
                    "quantity": trade_result.get("quantity"),
                    "price": trade_result.get("price"),
                    "cost": trade_result.get("cost"),
                    "profit_loss": trade_result.get("profit_loss"),
                    "status": trade_result.get("status")
                },
                "performance_metrics": {
                    "total_trades": total_trades,
                    "success_rate": f"{success_rate:.2f}%",
                    "total_profit_loss": self.trading_stats["total_profit_loss"],
                    "average_profit_per_trade": (
                        self.trading_stats["total_profit_loss"] / total_trades 
                        if total_trades > 0 else 0
                    )
                },
                "execution_method": trading_result.get("execution_method"),
                "generation_time": datetime.now().isoformat()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成交易报告失败: {e}")
            return {
                "report_type": "error",
                "error": str(e),
                "generation_time": datetime.now().isoformat()
            }
    
    async def start_automation(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动自动化任务"""
        try:
            automation_id = f"yuheng_auto_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.automation_tasks[automation_id] = {
                "config": config,
                "status": "running",
                "start_time": datetime.now().isoformat()
            }
            
            self.is_active = True
            
            logger.info(f"玉衡星自动化任务启动: {automation_id}")
            
            return {
                "success": True,
                "automation_id": automation_id,
                "message": "玉衡星自动化任务启动成功"
            }
            
        except Exception as e:
            logger.error(f"启动玉衡星自动化任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        return {
            "system_name": self.system_name,
            "version": self.version,
            "is_active": self.is_active,
            "active_tasks": len([t for t in self.automation_tasks.values() if t["status"] == "running"]),
            "total_tasks": len(self.automation_tasks),
            "trading_stats": self.trading_stats,
            "status_time": datetime.now().isoformat()
        }


    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
        """调用yuheng_star专用DeepSeek分析"""
        try:
            from roles.yuheng_star.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建角色专用提示词
            role_prompt = f"{role_setting}\n\n请分析：{prompt}"
            if context_data:
                try:
                    import json
                    context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                    role_prompt += f"\n\n上下文数据：{context_str}"
                except:
                    role_prompt += f"\n\n上下文数据：{str(context_data)}"
            
            # 调用DeepSeek服务
            messages = [
                {"role": "system", "content": role_setting},
                {"role": "user", "content": prompt}
            ]
            
            result = await deepseek_service.chat_completion(messages, **config)
            
            return {
                "success": result.get("success", False),
                "analysis": result.get("response", ""),
                "role": "yuheng_star",
                "context_type": context_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "role": "yuheng_star",
                "timestamp": datetime.now().isoformat()
            }
    
    async def store_memory(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
        """存储记忆到传奇记忆系统"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            
            result = await legendary_memory_interface.store_memory(
                content=content,
                message_type=memory_type,
                role_source="yuheng_star",
                priority=priority,
                metadata=metadata or {}
            )
            
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def retrieve_memories(self, query: str, limit: int = 10) -> list:
        """从传奇记忆系统检索记忆"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            
            memories = await legendary_memory_interface.search_memories(
                query=query,
                role_filter="yuheng_star",
                limit=limit
            )
            
            return memories
            
        except Exception as e:
            return []
    
    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        """记录绩效到监控系统"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor
            
            result = await star_performance_monitor.record_performance(
                star_name="yuheng_star",
                metric_type=metric_name,
                value=value,
                context=context or {}
            )
            
            return {"success": result, "metric": metric_name, "value": value}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_performance_stats(self) -> dict:
        """获取角色绩效统计"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor
            
            return star_performance_monitor.get_star_performance("yuheng_star")
            
        except Exception as e:
            return {"error": str(e)}

# 全局实例
yuheng_automation_system = YuhengAutomationSystem()
