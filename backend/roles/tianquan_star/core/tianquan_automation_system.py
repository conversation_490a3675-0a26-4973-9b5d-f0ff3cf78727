#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天权星自动化系统
负责投资决策制定和四星协作的自动化执行
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

logger = logging.getLogger(__name__)

class TianquanAutomationSystem:
    """天权星自动化系统"""
    
    def __init__(self):
        self.system_name = "TianquanAutomationSystem"
        self.version = "1.0.0"
        self.is_active = False
        self.automation_tasks = {}
        
        # 核心服务引用
        self.decision_service = None
        self.strategy_service = None
        self.collaboration_service = None
        
        # 初始化核心服务
        self._init_core_services()
        
        logger.info(f"天权星自动化系统 v{self.version} 初始化完成")
    
    def _init_core_services(self):
        """初始化核心服务"""
        try:
            # 导入天权星核心服务
            from ..services.strategic_decision_service import strategic_decision_service
            self.decision_service = strategic_decision_service
            logger.info("✅ 战略决策服务初始化成功")
        except ImportError as e:
            logger.warning(f"⚠️ 战略决策服务导入失败: {e}")
        
        try:
            from ..services.strategy_management_service import strategy_management_service
            self.strategy_service = strategy_management_service
            logger.info("✅ 策略管理服务初始化成功")
        except ImportError as e:
            logger.warning(f"⚠️ 策略管理服务导入失败: {e}")
        
        try:
            from ..services.four_stars_collaboration_service import four_stars_collaboration_service
            self.collaboration_service = four_stars_collaboration_service
            logger.info("✅ 四星协作服务初始化成功")
        except ImportError as e:
            logger.warning(f"⚠️ 四星协作服务导入失败: {e}")
    
    async def execute_decision_automation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行决策自动化"""
        try:
            task_type = context.get("task_type", "investment_decision")
            stock_code = context.get("stock_code", "000001.XSHE")
            session_id = context.get("session_id", "default_session")
            
            logger.info(f"🎯 天权星开始执行决策自动化: {task_type} - {stock_code}")
            
            if task_type == "investment_decision":
                return await self._execute_investment_decision(context)
            elif task_type == "strategy_coordination":
                return await self._execute_strategy_coordination(context)
            elif task_type == "four_stars_collaboration":
                return await self._execute_four_stars_collaboration(context)
            else:
                return await self._execute_general_decision(context)
                
        except Exception as e:
            logger.error(f"天权星决策自动化执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "automation_result": {
                    "decision": "hold",
                    "confidence": 0.5,
                    "reasoning": "自动化系统异常，采用保守策略"
                }
            }
    
    async def _execute_investment_decision(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行投资决策"""
        try:
            stock_code = context.get("stock_code")
            risk_preference = context.get("risk_preference", "moderate")
            market_context = context.get("market_context", {})
            
            if self.decision_service:
                # 使用真实的决策服务
                decision_result = await self.decision_service.make_investment_decision(
                    stock_code=stock_code,
                    risk_preference=risk_preference,
                    market_context=market_context
                )
                
                return {
                    "success": True,
                    "automation_result": decision_result,
                    "execution_time": datetime.now().isoformat(),
                    "data_source": "strategic_decision_service"
                }
            else:
                # 降级决策逻辑
                return {
                    "success": True,
                    "automation_result": {
                        "decision": "hold",
                        "confidence": 0.6,
                        "reasoning": "决策服务不可用，采用保守策略",
                        "risk_assessment": {"level": "medium", "score": 0.5}
                    },
                    "execution_time": datetime.now().isoformat(),
                    "data_source": "fallback_logic"
                }
                
        except Exception as e:
            logger.error(f"投资决策执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_strategy_coordination(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行策略协调"""
        try:
            strategy_type = context.get("strategy_type", "balanced")
            target_stocks = context.get("target_stocks", [])
            
            if self.strategy_service:
                # 使用真实的策略服务
                coordination_result = await self.strategy_service.coordinate_strategies(
                    strategy_type=strategy_type,
                    target_stocks=target_stocks
                )
                
                return {
                    "success": True,
                    "automation_result": coordination_result,
                    "execution_time": datetime.now().isoformat(),
                    "data_source": "strategy_management_service"
                }
            else:
                # 降级协调逻辑
                return {
                    "success": True,
                    "automation_result": {
                        "coordinated_strategies": [
                            {
                                "strategy_name": "平衡策略",
                                "allocation": 0.6,
                                "risk_level": "medium"
                            },
                            {
                                "strategy_name": "保守策略",
                                "allocation": 0.4,
                                "risk_level": "low"
                            }
                        ],
                        "total_allocation": 1.0,
                        "coordination_status": "completed"
                    },
                    "execution_time": datetime.now().isoformat(),
                    "data_source": "fallback_logic"
                }
                
        except Exception as e:
            logger.error(f"策略协调执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_four_stars_collaboration(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行四星协作"""
        try:
            collaboration_type = context.get("collaboration_type", "comprehensive_analysis")
            stock_code = context.get("stock_code")
            
            if self.collaboration_service:
                # 使用真实的协作服务
                collaboration_result = await self.collaboration_service.coordinate_four_stars_analysis(
                    stock_code=stock_code,
                    analysis_type=collaboration_type
                )
                
                return {
                    "success": True,
                    "automation_result": collaboration_result,
                    "execution_time": datetime.now().isoformat(),
                    "data_source": "four_stars_collaboration_service"
                }
            else:
                # 降级协作逻辑
                return {
                    "success": True,
                    "automation_result": {
                        "collaboration_status": "completed",
                        "participating_stars": ["天枢星", "天玑星", "天璇星", "玉衡星"],
                        "analysis_summary": {
                            "tianshu_news": "市场情绪中性",
                            "tianji_risk": "风险可控",
                            "tianxuan_technical": "技术面平稳",
                            "yuheng_execution": "执行条件良好"
                        },
                        "final_recommendation": "持有观望"
                    },
                    "execution_time": datetime.now().isoformat(),
                    "data_source": "fallback_logic"
                }
                
        except Exception as e:
            logger.error(f"四星协作执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_general_decision(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行通用决策"""
        try:
            return {
                "success": True,
                "automation_result": {
                    "decision_type": "general",
                    "recommendation": "基于当前市场环境，建议保持现有仓位",
                    "confidence": 0.7,
                    "next_review_time": (datetime.now() + timedelta(hours=4)).isoformat()
                },
                "execution_time": datetime.now().isoformat(),
                "data_source": "general_logic"
            }
            
        except Exception as e:
            logger.error(f"通用决策执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        try:
            return {
                "system_name": self.system_name,
                "version": self.version,
                "is_active": self.is_active,
                "active_tasks": len(self.automation_tasks),
                "core_services": {
                    "decision_service": self.decision_service is not None,
                    "strategy_service": self.strategy_service is not None,
                    "collaboration_service": self.collaboration_service is not None
                },
                "last_update": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取自动化状态失败: {e}")
            return {
                "system_name": self.system_name,
                "error": str(e),
                "status": "error"
            }
    
    async def start_automation(self) -> Dict[str, Any]:
        """启动自动化"""
        try:
            self.is_active = True
            logger.info("🚀 天权星自动化系统已启动")
            
            return {
                "success": True,
                "message": "天权星自动化系统启动成功",
                "start_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"启动自动化失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_automation(self) -> Dict[str, Any]:
        """停止自动化"""
        try:
            self.is_active = False
            self.automation_tasks.clear()
            logger.info("🛑 天权星自动化系统已停止")
            
            return {
                "success": True,
                "message": "天权星自动化系统停止成功",
                "stop_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"停止自动化失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
        """调用天权星专用DeepSeek分析"""
        try:
            from roles.tianquan_star.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service

            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()

            # 构建角色专用提示词
            role_prompt = f"{role_setting}\n\n请分析：{prompt}"
            if context_data:
                try:
                    import json
                    context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                    role_prompt += f"\n\n上下文数据：{context_str}"
                except:
                    role_prompt += f"\n\n上下文数据：{str(context_data)}"

            # 调用DeepSeek服务
            messages = [
                {"role": "system", "content": role_setting},
                {"role": "user", "content": prompt}
            ]

            result = await deepseek_service.chat_completion(messages, **config)

            return {
                "success": result.get("success", False),
                "analysis": result.get("response", ""),
                "role": "tianquan_star",
                "context_type": context_type,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "role": "tianquan_star",
                "timestamp": datetime.now().isoformat()
            }

    async def store_memory(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
        """存储记忆到传奇记忆系统"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            from core.domain.memory.legendary.models import MessageType, MemoryPriority

            
            # 映射消息类型
            message_type_mapping = {
                "analysis": MessageType.MARKET_ANALYSIS,
                "news": MessageType.NEWS_UPDATE,
                "general": MessageType.GENERAL,
                "system": MessageType.SYSTEM_NOTIFICATION,
                "risk": MessageType.RISK_ASSESSMENT,
                "strategy": MessageType.STRATEGY_PLANNING,
                "execution": MessageType.TRADING_EXECUTION
            }
            
            # 映射优先级
            priority_mapping = {
                "high": MemoryPriority.HIGH,
                "normal": MemoryPriority.NORMAL,
                "low": MemoryPriority.LOW
            }

            result = await legendary_memory_interface.add_memory(
                content=content,
                message_type=memory_type,
                role="天权星",
                priority=priority,
                metadata=metadata or {}
            )

            return {"success": result.success, "memory_id": result.memory_id}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def retrieve_memories(self, query: str, limit: int = 10) -> list:
        """从传奇记忆系统检索记忆"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface

            
            

            
            memories = await legendary_memory_interface.search_memories(
                query=query,
                role_filter="tianquan_star",
                limit=limit
            )

            return memories

        except Exception as e:
            return []

    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            result = await star_performance_monitor.record_performance(
                star_name="tianquan_star",
                metric_type=metric_name,
                value=value,
            )


        except Exception as e:

            return {"success": False, "error": str(e)}

    def get_performance_stats(self) -> dict:
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            return star_performance_monitor.get_star_performance("tianquan_star")

        except Exception as e:

            return {"success": False, "error": str(e)}

# 创建全局实例
tianquan_automation_system = TianquanAutomationSystem()

# 导出
__all__ = ["tianquan_automation_system", "TianquanAutomationSystem"]
