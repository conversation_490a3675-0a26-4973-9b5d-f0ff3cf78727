#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星自动化系统
股票检测员的自动化流程
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class KaiyangAutomationSystem:
    """开阳星自动化系统"""
    
    def __init__(self):
        self.service_name = "KaiyangAutomationSystem"
        self.version = "1.0.0"
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """初始化自动化系统"""
        try:
            # 初始化股票选择服务
            from .stock_selection_service import stock_selection_service
            self.stock_selector = stock_selection_service
            
            self.is_initialized = True
            logger.info(f"{self.service_name} v{self.version} 初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"{self.service_name} 初始化失败: {e}")
            return False
    
    async def execute_stock_selection_automation(self, mode: str = "learning") -> Dict[str, Any]:
        """执行股票选择自动化"""
        try:
            if not self.is_initialized:
                await self.initialize()

            # 根据模式选择股票
            if mode == "learning":
                # 学习模式：选择1只股票
                result = await self.stock_selector.select_learning_stocks(count=1)
            else:
                # 实盘模式：选择多只股票
                result = await self.stock_selector.select_portfolio_stocks(count=10)

            logger.info(f"✅ 开阳星自动化选股完成: {mode}模式")
            return result

        except Exception as e:
            logger.error(f"❌ 开阳星自动化选股失败: {e}")
            return {"success": False, "error": str(e)}

    async def start_automation(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动自动化任务"""
        try:
            if not self.is_initialized:
                await self.initialize()

            automation_id = f"kaiyang_auto_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            logger.info(f"开阳星自动化任务启动: {automation_id}")

            return {
                "success": True,
                "automation_id": automation_id,
                "message": "开阳星自动化任务启动成功"
            }

        except Exception as e:
            logger.error(f"启动开阳星自动化任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "is_initialized": self.is_initialized,
            "is_active": self.is_initialized,
            "last_update": datetime.now().isoformat()
        }


    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: dict = None) -> dict:
        """调用kaiyang_star专用DeepSeek分析"""
        try:
            from roles.kaiyang_star.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建角色专用提示词
            role_prompt = f"{role_setting}\n\n请分析：{prompt}"
            if context_data:
                try:
                    import json
                    context_str = json.dumps(context_data, ensure_ascii=False, default=str)
                    role_prompt += f"\n\n上下文数据：{context_str}"
                except:
                    role_prompt += f"\n\n上下文数据：{str(context_data)}"
            
            # 调用DeepSeek服务
            messages = [
                {"role": "system", "content": role_setting},
                {"role": "user", "content": prompt}
            ]
            
            result = await deepseek_service.chat_completion(messages, **config)
            
            return {
                "success": result.get("success", False),
                "analysis": result.get("response", ""),
                "role": "kaiyang_star",
                "context_type": context_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "role": "kaiyang_star",
                "timestamp": datetime.now().isoformat()
            }
    
    async def store_memory(self, content: str, memory_type: str = "analysis", priority: str = "normal", metadata: dict = None) -> dict:
        """存储记忆到传奇记忆系统"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface
            from core.domain.memory.legendary.models import MessageType, MemoryPriority
            
            
            # 映射消息类型
            message_type_mapping = {
                "analysis": MessageType.MARKET_ANALYSIS,
                "news": MessageType.NEWS_UPDATE,
                "general": MessageType.GENERAL,
                "system": MessageType.SYSTEM_NOTIFICATION,
                "risk": MessageType.RISK_ASSESSMENT,
                "strategy": MessageType.STRATEGY_PLANNING,
                "execution": MessageType.TRADING_EXECUTION
            }
            
            # 映射优先级
            priority_mapping = {
                "high": MemoryPriority.HIGH,
                "normal": MemoryPriority.NORMAL,
                "low": MemoryPriority.LOW
            }

            result = await legendary_memory_interface.add_memory(
                content=content,
                role="开阳星",
                message_type=message_type_mapping.get(memory_type, MessageType.GENERAL),
                priority=priority_mapping.get(priority, MemoryPriority.NORMAL),
                metadata=metadata or {}
            )
            
            return {"success": result.success, "memory_id": result.memory_id}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def retrieve_memories(self, query: str, limit: int = 10) -> list:
        """从传奇记忆系统检索记忆"""
        try:
            from core.domain.memory.legendary.interface import legendary_memory_interface

            memories = await legendary_memory_interface.search_memories(
                query=query,
                role_filter="kaiyang_star",
                limit=limit
            )
            
            return memories
            
        except Exception as e:
            return []
    
    async def record_performance(self, metric_name: str, value: float, context: dict = None) -> dict:
        """记录绩效到监控系统"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            result = await star_performance_monitor.record_performance(
                star_name="kaiyang_star",
                metric_type=metric_name,
                value=value,
                context=context or {}
            )

            return {"success": result, "metric": metric_name, "value": value}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_performance_stats(self) -> dict:
        """获取角色绩效统计"""
        try:
            from core.performance.star_performance_monitor import star_performance_monitor

            return star_performance_monitor.get_star_performance("kaiyang_star")

        except Exception as e:
            return {"error": str(e)}

# 全局实例
kaiyang_automation_system = KaiyangAutomationSystem()
